#!/bin/bash

# Pulse Guard Docker 部署脚本
set -e

echo "🚀 开始部署 Pulse Guard..."

# 检查 Docker 是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装，请先安装 Docker"
    exit 1
fi

# 检查 Docker Compose 是否安装
if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
    echo "❌ Docker Compose 未安装，请先安装 Docker Compose"
    exit 1
fi

# 检查 .env 文件是否存在
if [ ! -f ".env" ]; then
    echo "📝 创建 .env 文件..."
    cp .env.example .env
    echo "⚠️  请编辑 .env 文件，配置必要的环境变量："
    echo "   - GITHUB_TOKEN"
    echo "   - DEEPSEEK_API_KEY"
    echo "   - WEBHOOK_SECRET"
    echo ""
    read -p "配置完成后按 Enter 继续..."
fi

# 创建日志目录
mkdir -p logs

# 构建并启动服务
echo "🔨 构建 Docker 镜像..."
docker-compose build

echo "🚀 启动服务..."
docker-compose up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo "📊 检查服务状态..."
docker-compose ps

# 检查 Web 服务健康状态
echo "🔍 检查 Web 服务..."
if curl -f http://localhost:8000/ > /dev/null 2>&1; then
    echo "✅ Web 服务启动成功"
else
    echo "❌ Web 服务启动失败"
    echo "查看日志: docker-compose logs web"
fi

# 检查 Redis 连接
echo "🔍 检查 Redis 服务..."
if docker-compose exec redis redis-cli ping > /dev/null 2>&1; then
    echo "✅ Redis 服务正常"
else
    echo "❌ Redis 服务异常"
    echo "查看日志: docker-compose logs redis"
fi

# 检查 Celery Worker
echo "🔍 检查 Celery Worker..."
if docker-compose exec worker .venv/bin/celery -A pulse_guard.worker.celery_app inspect ping > /dev/null 2>&1; then
    echo "✅ Celery Worker 正常"
else
    echo "⚠️  Celery Worker 可能需要更多时间启动"
    echo "查看日志: docker-compose logs worker"
fi

echo ""
echo "🎉 部署完成！"
echo ""
echo "📋 服务信息："
echo "   Web 服务: http://localhost:8000"
echo "   API 文档: http://localhost:8000/docs"
echo "   GitHub Webhook: http://localhost:8000/api/webhook/github"
echo "   Gitee Webhook: http://localhost:8000/api/webhook/gitee"
echo ""
echo "📝 常用命令："
echo "   查看服务状态: docker-compose ps"
echo "   查看日志: docker-compose logs -f [service_name]"
echo "   停止服务: docker-compose down"
echo "   重启服务: docker-compose restart"
echo "   启动监控: docker-compose --profile monitoring up -d flower"
echo ""
echo "🔧 如需启用 Flower 监控界面："
echo "   docker-compose --profile monitoring up -d flower"
echo "   访问: http://localhost:5555"
