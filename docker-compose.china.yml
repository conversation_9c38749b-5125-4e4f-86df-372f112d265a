version: '3.8'

services:
  # Redis 服务 - 使用阿里云镜像
  redis:
    image: registry.cn-hangzhou.aliyuncs.com/library/redis:7-alpine
    container_name: pulse-guard-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3

  # Web 服务
  web:
    build:
      context: .
      dockerfile: Dockerfile.china
    container_name: pulse-guard-web
    ports:
      - "8000:8000"
    environment:
      - REDIS_URL=redis://redis:6379/0
    env_file:
      - .env
    depends_on:
      redis:
        condition: service_healthy
    volumes:
      - ./logs:/app/logs
      - ./.env:/app/.env
      - ./config.toml:/app/config.toml
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/"]
      interval: 30s
      timeout: 10s
      retries: 3
    command: [".venv/bin/uvicorn", "pulse_guard.main:app", "--host", "0.0.0.0", "--port", "8000"]

  # Celery Worker 服务
  worker:
    build:
      context: .
      dockerfile: Dockerfile.china
    container_name: pulse-guard-worker
    environment:
      - REDIS_URL=redis://redis:6379/0
    env_file:
      - .env
    depends_on:
      redis:
        condition: service_healthy
    volumes:
      - ./logs:/app/logs
      - ./.env:/app/.env
      - ./config.toml:/app/config.toml
    restart: unless-stopped
    command: [".venv/bin/celery", "-A", "pulse_guard.worker.celery_app", "worker", "--loglevel=info", "--concurrency=2"]

  # Celery Flower 监控服务 (可选)
  flower:
    build:
      context: .
      dockerfile: Dockerfile.china
    container_name: pulse-guard-flower
    ports:
      - "5555:5555"
    environment:
      - REDIS_URL=redis://redis:6379/0
    env_file:
      - .env
    depends_on:
      redis:
        condition: service_healthy
    volumes:
      - ./.env:/app/.env
    restart: unless-stopped
    command: [".venv/bin/celery", "-A", "pulse_guard.worker.celery_app", "flower", "--port=5555"]
    profiles:
      - monitoring

volumes:
  redis_data:
    driver: local

networks:
  default:
    name: pulse-guard-network
