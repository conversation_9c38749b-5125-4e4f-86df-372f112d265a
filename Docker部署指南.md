# Pulse Guard Docker 部署指南

## 概述

本指南介绍如何使用 Docker 和 Docker Compose 快速部署 Pulse Guard 应用。Docker 方式提供了以下优势：

- **环境一致性**：确保开发、测试、生产环境完全一致
- **快速部署**：一键启动所有服务
- **易于管理**：统一的服务管理和监控
- **资源隔离**：各服务独立运行，互不干扰

## 架构说明

Docker Compose 配置包含以下服务：

- **redis**: Redis 数据库服务
- **web**: FastAPI Web 应用服务
- **worker**: Celery 异步任务处理服务
- **flower**: Celery 监控服务（可选）

## 前置要求

### 1. 安装 Docker

#### Ubuntu/Debian
```bash
# 更新包索引
sudo apt update

# 安装必要的包
sudo apt install apt-transport-https ca-certificates curl gnupg lsb-release

# 添加 Docker 官方 GPG 密钥
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg

# 添加 Docker 仓库
echo "deb [arch=amd64 signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

# 安装 Docker
sudo apt update
sudo apt install docker-ce docker-ce-cli containerd.io

# 启动 Docker 服务
sudo systemctl enable docker
sudo systemctl start docker
```

#### CentOS/RHEL
```bash
# 安装必要的包
sudo dnf install -y dnf-utils

# 添加 Docker 仓库
sudo dnf config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo

# 安装 Docker
sudo dnf install docker-ce docker-ce-cli containerd.io

# 启动 Docker 服务
sudo systemctl enable docker
sudo systemctl start docker
```

#### macOS
```bash
# 使用 Homebrew 安装
brew install --cask docker

# 或者下载 Docker Desktop
# https://www.docker.com/products/docker-desktop
```

### 2. 安装 Docker Compose

#### Linux
```bash
# 下载 Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose

# 添加执行权限
sudo chmod +x /usr/local/bin/docker-compose

# 验证安装
docker-compose --version
```

#### macOS/Windows
Docker Desktop 已包含 Docker Compose，无需单独安装。

### 3. 配置用户权限（Linux）

```bash
# 将当前用户添加到 docker 组
sudo usermod -aG docker $USER

# 重新登录或执行以下命令
newgrp docker

# 验证权限
docker run hello-world
```

## 快速部署

### 1. 使用自动部署脚本（推荐）

```bash
# 给脚本添加执行权限
chmod +x docker-deploy.sh

# 运行部署脚本
./docker-deploy.sh
```

脚本会自动：
- 检查 Docker 环境
- 创建 .env 文件（如果不存在）
- 构建 Docker 镜像
- 启动所有服务
- 检查服务健康状态

### 2. 手动部署

#### 步骤 1: 配置环境变量

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量文件
vim .env
```

必须配置的环境变量：
```bash
# GitHub API Token
GITHUB_TOKEN=ghp_your_github_token_here

# LLM API Key
DEEPSEEK_API_KEY=your_deepseek_api_key_here

# Webhook Secret
WEBHOOK_SECRET=your_webhook_secret_here

# 其他可选配置...
```

#### 步骤 2: 构建和启动服务

```bash
# 构建镜像
docker-compose build

# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps
```

## 服务管理

### 基本命令

```bash
# 启动所有服务
docker-compose up -d

# 停止所有服务
docker-compose down

# 重启服务
docker-compose restart

# 重启特定服务
docker-compose restart web worker

# 查看服务状态
docker-compose ps

# 查看服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f web
docker-compose logs -f worker
```

### 扩展服务

```bash
# 扩展 Worker 服务到 3 个实例
docker-compose up -d --scale worker=3

# 查看扩展后的服务
docker-compose ps
```

### 更新应用

```bash
# 拉取最新代码
git pull

# 重新构建镜像
docker-compose build

# 重启服务
docker-compose up -d
```

## 监控和调试

### 1. 启用 Flower 监控

```bash
# 启动 Flower 监控服务
docker-compose --profile monitoring up -d flower

# 访问监控界面
open http://localhost:5555
```

Flower 提供以下功能：
- 实时任务监控
- Worker 状态查看
- 任务历史记录
- 性能统计

### 2. 查看日志

```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f web
docker-compose logs -f worker
docker-compose logs -f redis

# 查看最近 100 行日志
docker-compose logs --tail=100 web
```

### 3. 进入容器调试

```bash
# 进入 Web 容器
docker-compose exec web bash

# 进入 Worker 容器
docker-compose exec worker bash

# 进入 Redis 容器
docker-compose exec redis redis-cli
```

### 4. 健康检查

```bash
# 检查 Web 服务
curl http://localhost:8000/

# 检查 Redis 连接
docker-compose exec redis redis-cli ping

# 检查 Celery Worker
docker-compose exec worker .venv/bin/celery -A pulse_guard.worker.celery_app inspect ping
```

## 配置优化

### 1. 生产环境配置

创建 `docker-compose.prod.yml`：

```yaml
version: '3.8'

services:
  web:
    restart: always
    environment:
      - ENVIRONMENT=production
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  worker:
    restart: always
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  redis:
    restart: always
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
```

使用生产配置：
```bash
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
```

### 2. 资源限制

在 `docker-compose.yml` 中添加资源限制：

```yaml
services:
  web:
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
```

### 3. 网络配置

```yaml
networks:
  pulse-guard-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
```

## 数据持久化

### 1. 数据卷管理

```bash
# 查看数据卷
docker volume ls

# 备份 Redis 数据
docker run --rm -v pulse-guard_redis_data:/data -v $(pwd):/backup alpine tar czf /backup/redis-backup.tar.gz -C /data .

# 恢复 Redis 数据
docker run --rm -v pulse-guard_redis_data:/data -v $(pwd):/backup alpine tar xzf /backup/redis-backup.tar.gz -C /data
```

### 2. 日志持久化

日志文件通过卷挂载到宿主机的 `./logs` 目录，可以直接访问：

```bash
# 查看日志文件
ls -la logs/

# 清理旧日志
find logs/ -name "*.log" -mtime +7 -delete
```

## 故障排除

### 常见问题

1. **端口冲突**
   ```bash
   # 检查端口占用
   netstat -tulpn | grep :8000
   
   # 修改端口映射
   # 在 docker-compose.yml 中修改 ports 配置
   ```

2. **内存不足**
   ```bash
   # 检查容器资源使用
   docker stats
   
   # 增加系统内存或调整容器资源限制
   ```

3. **网络连接问题**
   ```bash
   # 检查容器网络
   docker network ls
   docker network inspect pulse-guard-network
   
   # 重建网络
   docker-compose down
   docker network prune
   docker-compose up -d
   ```

### 日志分析

```bash
# 查看错误日志
docker-compose logs | grep ERROR

# 查看特定时间段的日志
docker-compose logs --since="2024-01-01T00:00:00" --until="2024-01-01T23:59:59"

# 导出日志到文件
docker-compose logs > pulse-guard.log
```

## 安全配置

### 1. 环境变量安全

```bash
# 设置 .env 文件权限
chmod 600 .env

# 使用 Docker secrets（生产环境推荐）
echo "your_secret" | docker secret create github_token -
```

### 2. 网络安全

```yaml
# 在 docker-compose.yml 中配置内部网络
networks:
  internal:
    internal: true
  external:
    
services:
  web:
    networks:
      - external
      - internal
  worker:
    networks:
      - internal
  redis:
    networks:
      - internal
```

### 3. 容器安全

```dockerfile
# 在 Dockerfile 中使用非 root 用户
RUN adduser --disabled-password --gecos '' appuser
USER appuser
```

## 性能调优

### 1. Redis 优化

```yaml
services:
  redis:
    command: redis-server --maxmemory 512mb --maxmemory-policy allkeys-lru
```

### 2. Worker 并发优化

```yaml
services:
  worker:
    command: [".venv/bin/celery", "-A", "pulse_guard.worker.celery_app", "worker", "--loglevel=info", "--concurrency=4", "--prefetch-multiplier=1"]
```

### 3. Web 服务优化

```yaml
services:
  web:
    command: [".venv/bin/gunicorn", "pulse_guard.main:app", "-w", "4", "-k", "uvicorn.workers.UvicornWorker", "--bind", "0.0.0.0:8000"]
```

---

通过以上配置，您可以快速、安全、高效地部署 Pulse Guard 应用。Docker 方式大大简化了部署流程，提高了系统的可维护性和可扩展性。
