# 使用阿里云镜像源
FROM registry.cn-hangzhou.aliyuncs.com/library/python:3.12-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV PATH="/app/.venv/bin:$PATH"

# 配置 apt 使用国内源
RUN sed -i 's/deb.debian.org/mirrors.ustc.edu.cn/g' /etc/apt/sources.list.d/debian.sources && \
    sed -i 's/security.debian.org/mirrors.ustc.edu.cn/g' /etc/apt/sources.list.d/debian.sources

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    git \
    curl \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# 安装 uv，使用国内源
RUN curl -sSf https://install.uv.dev | sh
ENV PATH="/root/.local/bin:$PATH"

# 配置 pip 使用国内源
RUN mkdir -p ~/.pip && \
    echo '[global]' > ~/.pip/pip.conf && \
    echo 'index-url = https://pypi.tuna.tsinghua.edu.cn/simple' >> ~/.pip/pip.conf && \
    echo 'trusted-host = pypi.tuna.tsinghua.edu.cn' >> ~/.pip/pip.conf

# 复制依赖文件
COPY pyproject.toml requirements.txt ./
COPY .env.example .env

# 创建虚拟环境并安装依赖
RUN uv venv && \
    . .venv/bin/activate && \
    uv pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -e .

# 复制项目代码
COPY . .

# 创建日志目录
RUN mkdir -p logs

# 设置权限
RUN chmod +x .venv/bin/*

# 暴露端口
EXPOSE 8000

# 默认启动 web 服务
CMD [".venv/bin/uvicorn", "pulse_guard.main:app", "--host", "0.0.0.0", "--port", "8000"]
