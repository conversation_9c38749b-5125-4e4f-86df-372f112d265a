FROM python:3.12-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV PATH="/app/.venv/bin:$PATH"

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    git \
    curl \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# 安装 uv
RUN curl -sSf https://install.uv.dev | sh
ENV PATH="/root/.local/bin:$PATH"

# 复制依赖文件
COPY pyproject.toml requirements.txt ./
COPY .env.example .env

# 创建虚拟环境并安装依赖
RUN uv venv && \
    . .venv/bin/activate && \
    uv pip install -e .

# 复制项目代码
COPY . .

# 创建日志目录
RUN mkdir -p logs

# 设置权限
RUN chmod +x .venv/bin/*

# 暴露端口
EXPOSE 8000

# 默认启动 web 服务
CMD [".venv/bin/uvicorn", "pulse_guard.main:app", "--host", "0.0.0.0", "--port", "8000"]
