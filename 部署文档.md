# Pulse Guard 部署文档

## 概述

Pulse Guard 是一个基于 FastAPI + Celery + Redis 的自动化 PR 代码质量审查工具，支持 GitHub 和 Gitee 平台。本文档详细介绍了如何在不同环境中部署和配置该应用。

## 系统要求

### 硬件要求
- **CPU**: 2核心以上
- **内存**: 4GB 以上（推荐 8GB）
- **存储**: 20GB 以上可用空间
- **网络**: 稳定的互联网连接

### 软件要求
- **操作系统**: Linux (Ubuntu 20.04+/CentOS 8+), macOS 10.15+, Windows 10+
- **Python**: 3.12 或更高版本
- **Redis**: 6.0 或更高版本
- **Git**: 2.20 或更高版本

## 环境准备

### 1. 安装 Python 3.12+

#### Ubuntu/Debian
```bash
sudo apt update
sudo apt install python3.12 python3.12-venv python3.12-dev
```

#### CentOS/RHEL
```bash
sudo dnf install python3.12 python3.12-devel
```

#### macOS
```bash
brew install python@3.12
```

### 2. 安装 Redis

#### Ubuntu/Debian
```bash
sudo apt install redis-server
sudo systemctl enable redis-server
sudo systemctl start redis-server
```

#### CentOS/RHEL
```bash
sudo dnf install redis
sudo systemctl enable redis
sudo systemctl start redis
```

#### macOS
```bash
brew install redis
brew services start redis
```

### 3. 安装 uv (推荐的包管理器)

```bash
curl -sSf https://install.uv.dev | sh
source ~/.bashrc  # 或重新打开终端
```

## 项目部署

### 1. 获取项目代码

```bash
git clone <repository-url>
cd pulse-guard
```

### 2. 创建虚拟环境和安装依赖

#### 使用 uv (推荐)
```bash
# 创建虚拟环境
uv venv

# 激活虚拟环境
source .venv/bin/activate  # Linux/macOS
# 或
.venv\Scripts\activate     # Windows

# 安装项目依赖
uv pip install -e .

# 安装开发依赖（可选）
uv pip install -e ".[dev]"
```

#### 使用传统 pip
```bash
python -m venv .venv
source .venv/bin/activate  # Linux/macOS
pip install -e .
```

### 3. 配置环境变量

复制环境变量模板并配置：

```bash
cp .env.example .env
```

编辑 `.env` 文件，配置以下关键参数：

```bash
# GitHub API Token (必需)
GITHUB_TOKEN=ghp_your_github_token_here

# Gitee API Token (如果使用 Gitee)
GITEE_ACCESS_TOKEN=your_gitee_access_token_here

# LLM API Keys (必需)
DEEPSEEK_API_KEY=your_deepseek_api_key_here

# Webhook Secrets (生产环境必需)
WEBHOOK_SECRET=your_github_webhook_secret_here
GITEE_WEBHOOK_SECRET=your_gitee_webhook_secret_here

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# LangSmith Configuration (可选，用于调试)
LANGSMITH_TRACING=false
LANGSMITH_API_KEY=your_langsmith_api_here
LANGSMITH_PROJECT=pulse-guard
```

### 4. 配置应用参数

编辑 `config.toml` 文件：

```toml
[llm]
provider = "deepseek"
model_name = "deepseek-chat"

[github]
api_base_url = "https://api.github.com"
development_mode = false         # 生产环境设为 false
verify_webhook_signature = true  # 生产环境设为 true

[gitee]
api_base_url = "https://gitee.com/api/v5"

[review]
types = ["code_quality", "security", "best_practices"]
max_files_per_review = 10
```

## 部署方式

### 开发环境部署

#### 1. 启动 Redis
```bash
redis-server
```

#### 2. 启动 Web 服务
```bash
# 使用 Makefile (推荐)
make run

# 或手动启动
uv run uvicorn pulse_guard.main:app --host 0.0.0.0 --port 8000 --reload
```

#### 3. 启动 Celery Worker
```bash
# 新开终端窗口
make worker

# 或手动启动
uv run celery -A pulse_guard.worker.celery_app worker --loglevel=info
```

#### 4. 配置本地开发环境
```bash
# 启动 ngrok 进行内网穿透
make ngrok

# 或手动启动
ngrok http 8000
```

### 生产环境部署

#### 1. 使用 Systemd 服务管理

创建 Web 服务配置文件：

```bash
sudo tee /etc/systemd/system/pulse-guard-web.service > /dev/null <<EOF
[Unit]
Description=Pulse Guard Web Service
After=network.target redis.service

[Service]
Type=exec
User=www-data
Group=www-data
WorkingDirectory=/opt/pulse-guard
Environment=PATH=/opt/pulse-guard/.venv/bin
ExecStart=/opt/pulse-guard/.venv/bin/uvicorn pulse_guard.main:app --host 0.0.0.0 --port 8000
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
EOF
```

创建 Celery Worker 服务配置文件：

```bash
sudo tee /etc/systemd/system/pulse-guard-worker.service > /dev/null <<EOF
[Unit]
Description=Pulse Guard Celery Worker
After=network.target redis.service

[Service]
Type=exec
User=www-data
Group=www-data
WorkingDirectory=/opt/pulse-guard
Environment=PATH=/opt/pulse-guard/.venv/bin
ExecStart=/opt/pulse-guard/.venv/bin/celery -A pulse_guard.worker.celery_app worker --loglevel=info
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
EOF
```

启动服务：

```bash
sudo systemctl daemon-reload
sudo systemctl enable pulse-guard-web pulse-guard-worker
sudo systemctl start pulse-guard-web pulse-guard-worker
```

#### 2. 使用 Nginx 反向代理

安装 Nginx：

```bash
sudo apt install nginx  # Ubuntu/Debian
sudo dnf install nginx  # CentOS/RHEL
```

配置 Nginx：

```bash
sudo tee /etc/nginx/sites-available/pulse-guard > /dev/null <<EOF
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
}
EOF

sudo ln -s /etc/nginx/sites-available/pulse-guard /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

### Docker 部署

创建 `Dockerfile`：

```dockerfile
FROM python:3.12-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    git \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 安装 uv
RUN curl -sSf https://install.uv.dev | sh
ENV PATH="/root/.local/bin:$PATH"

# 复制项目文件
COPY . .

# 安装 Python 依赖
RUN uv venv && \
    . .venv/bin/activate && \
    uv pip install -e .

# 暴露端口
EXPOSE 8000

# 启动命令
CMD [".venv/bin/uvicorn", "pulse_guard.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

创建 `docker-compose.yml`：

```yaml
version: '3.8'

services:
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  web:
    build: .
    ports:
      - "8000:8000"
    environment:
      - REDIS_URL=redis://redis:6379/0
    env_file:
      - .env
    depends_on:
      - redis
    volumes:
      - .:/app

  worker:
    build: .
    command: .venv/bin/celery -A pulse_guard.worker.celery_app worker --loglevel=info
    environment:
      - REDIS_URL=redis://redis:6379/0
    env_file:
      - .env
    depends_on:
      - redis
    volumes:
      - .:/app

volumes:
  redis_data:
```

启动 Docker 服务：

```bash
docker-compose up -d
```

## 配置 Webhook

### GitHub Webhook 配置

1. 进入 GitHub 仓库设置页面
2. 点击 "Webhooks" → "Add webhook"
3. 配置以下参数：
   - **Payload URL**: `https://your-domain.com/api/webhook/github`
   - **Content type**: `application/json`
   - **Secret**: 与 `.env` 中的 `WEBHOOK_SECRET` 一致
   - **Events**: 选择 "Pull requests"

### Gitee Webhook 配置

1. 进入 Gitee 仓库管理页面
2. 点击 "WebHooks" → "添加 WebHook"
3. 配置以下参数：
   - **URL**: `https://your-domain.com/api/webhook/gitee`
   - **密码**: 与 `.env` 中的 `GITEE_WEBHOOK_SECRET` 一致
   - **事件**: 选择 "Pull Request"

## 监控和日志

### 日志配置

应用日志默认输出到控制台，生产环境建议配置日志文件：

```python
# 在 pulse_guard/main.py 中添加文件日志处理器
import logging
from logging.handlers import RotatingFileHandler

# 配置文件日志
file_handler = RotatingFileHandler(
    'logs/pulse-guard.log', 
    maxBytes=10*1024*1024,  # 10MB
    backupCount=5
)
file_handler.setFormatter(
    logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
)
logging.getLogger().addHandler(file_handler)
```

### 服务状态监控

检查服务状态：

```bash
# 检查 Web 服务
curl http://localhost:8000/

# 检查 Systemd 服务状态
sudo systemctl status pulse-guard-web
sudo systemctl status pulse-guard-worker

# 检查 Redis 状态
redis-cli ping

# 查看 Celery 任务状态
uv run celery -A pulse_guard.worker.celery_app inspect active
```

## 故障排除

### 常见问题

1. **Redis 连接失败**
   ```bash
   # 检查 Redis 是否运行
   sudo systemctl status redis
   
   # 检查 Redis 连接
   redis-cli ping
   ```

2. **Celery Worker 无法启动**
   ```bash
   # 检查 Redis 连接
   # 检查环境变量配置
   # 查看详细错误日志
   uv run celery -A pulse_guard.worker.celery_app worker --loglevel=debug
   ```

3. **Webhook 接收失败**
   ```bash
   # 检查防火墙设置
   # 验证 Webhook URL 可访问性
   # 检查签名验证配置
   ```

### 日志查看

```bash
# 查看应用日志
tail -f logs/pulse-guard.log

# 查看 Systemd 服务日志
sudo journalctl -u pulse-guard-web -f
sudo journalctl -u pulse-guard-worker -f

# 查看 Nginx 日志
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log
```

## 安全配置

### 1. 环境变量安全

- 确保 `.env` 文件权限设置为 600
- 不要将敏感信息提交到版本控制系统
- 使用强密码和复杂的 API 密钥

### 2. 网络安全

- 配置防火墙，只开放必要端口
- 使用 HTTPS 加密传输
- 配置 Webhook 签名验证

### 3. 应用安全

```bash
# 设置文件权限
sudo chown -R www-data:www-data /opt/pulse-guard
sudo chmod -R 755 /opt/pulse-guard
sudo chmod 600 /opt/pulse-guard/.env
```

## 性能优化

### 1. Redis 优化

```bash
# 编辑 Redis 配置
sudo vim /etc/redis/redis.conf

# 优化配置项
maxmemory 2gb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

### 2. Celery 优化

```bash
# 增加 Worker 进程数
uv run celery -A pulse_guard.worker.celery_app worker --concurrency=4

# 配置任务路由和队列
# 在 celery_app.py 中添加
celery_app.conf.task_routes = {
    'pulse_guard.worker.tasks.process_pull_request': {'queue': 'pr_review'},
}
```

### 3. 应用优化

- 使用 Gunicorn 作为 WSGI 服务器
- 配置连接池
- 启用 HTTP 缓存
- 使用 CDN 加速静态资源

## 备份和恢复

### 数据备份

```bash
# Redis 数据备份
redis-cli BGSAVE
cp /var/lib/redis/dump.rdb /backup/redis-$(date +%Y%m%d).rdb

# 配置文件备份
tar -czf /backup/pulse-guard-config-$(date +%Y%m%d).tar.gz \
    .env config.toml
```

### 恢复流程

```bash
# 停止服务
sudo systemctl stop pulse-guard-web pulse-guard-worker

# 恢复 Redis 数据
sudo systemctl stop redis
sudo cp /backup/redis-20240617.rdb /var/lib/redis/dump.rdb
sudo systemctl start redis

# 恢复配置文件
tar -xzf /backup/pulse-guard-config-20240617.tar.gz

# 重启服务
sudo systemctl start pulse-guard-web pulse-guard-worker
```

## 更新和维护

### 应用更新

```bash
# 拉取最新代码
git pull origin main

# 更新依赖
uv pip install -e .

# 重启服务
sudo systemctl restart pulse-guard-web pulse-guard-worker
```

### 定期维护

```bash
# 清理日志文件
find logs/ -name "*.log" -mtime +30 -delete

# 清理 Redis 过期数据
redis-cli FLUSHDB

# 更新系统包
sudo apt update && sudo apt upgrade
```

---

## 联系支持

如果在部署过程中遇到问题，请：

1. 查看应用日志和错误信息
2. 检查配置文件是否正确
3. 确认所有依赖服务正常运行
4. 参考故障排除章节

更多技术支持，请参考项目 README.md 或提交 Issue。
