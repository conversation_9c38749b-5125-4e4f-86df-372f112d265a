#!/bin/bash

# Docker 镜像源配置脚本

echo "🔧 配置 Docker 镜像源..."

# 检测操作系统
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    # Linux 系统
    echo "检测到 Linux 系统"
    
    # 创建 Docker 配置目录
    sudo mkdir -p /etc/docker
    
    # 配置国内镜像源
    sudo tee /etc/docker/daemon.json > /dev/null <<EOF
{
  "registry-mirrors": [
    "https://docker.mirrors.ustc.edu.cn",
    "https://hub-mirror.c.163.com",
    "https://mirror.baidubce.com",
    "https://ccr.ccs.tencentyun.com"
  ],
  "insecure-registries": [],
  "debug": false,
  "experimental": false,
  "features": {
    "buildkit": true
  }
}
EOF
    
    # 重启 Docker 服务
    echo "重启 Docker 服务..."
    sudo systemctl daemon-reload
    sudo systemctl restart docker
    
    echo "✅ Docker 镜像源配置完成"
    
elif [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS 系统
    echo "检测到 macOS 系统"
    echo "请手动配置 Docker Desktop："
    echo "1. 打开 Docker Desktop"
    echo "2. 点击设置图标 -> Docker Engine"
    echo "3. 在配置中添加以下内容："
    echo ""
    cat <<EOF
{
  "registry-mirrors": [
    "https://docker.mirrors.ustc.edu.cn",
    "https://hub-mirror.c.163.com",
    "https://mirror.baidubce.com"
  ]
}
EOF
    echo ""
    echo "4. 点击 'Apply & Restart'"
    
else
    echo "未识别的操作系统: $OSTYPE"
    echo "请手动配置 Docker 镜像源"
fi

# 验证配置
echo ""
echo "🔍 验证 Docker 配置..."
docker info | grep -A 10 "Registry Mirrors" || echo "镜像源配置可能未生效，请重启 Docker"

echo ""
echo "📝 如果仍有问题，请尝试以下命令："
echo "   sudo systemctl restart docker  # Linux"
echo "   或重启 Docker Desktop          # macOS/Windows"
