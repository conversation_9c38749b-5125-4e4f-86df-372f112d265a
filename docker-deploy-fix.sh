#!/bin/bash

# Pulse Guard Docker 部署脚本 - 网络问题修复版
set -e

echo "🚀 开始部署 Pulse Guard (网络优化版)..."

# 检查 Docker 是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装，请先安装 Docker"
    exit 1
fi

# 检查 Docker Compose 是否安装
if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
    echo "❌ Docker Compose 未安装，请先安装 Docker Compose"
    exit 1
fi

# 检测网络连通性
echo "🔍 检测网络连通性..."
if ! curl -s --connect-timeout 5 https://registry-1.docker.io > /dev/null; then
    echo "⚠️  检测到 Docker Hub 连接问题，将使用国内镜像源"
    USE_CHINA_MIRROR=true
else
    echo "✅ Docker Hub 连接正常"
    USE_CHINA_MIRROR=false
fi

# 选择部署方式
if [ "$USE_CHINA_MIRROR" = true ]; then
    echo "📦 使用国内镜像源部署..."
    COMPOSE_FILE="docker-compose.china.yml"
    DOCKERFILE="Dockerfile.china"
    
    # 配置 Docker 镜像源
    echo "🔧 配置 Docker 镜像源..."
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        sudo mkdir -p /etc/docker
        sudo tee /etc/docker/daemon.json > /dev/null <<EOF
{
  "registry-mirrors": [
    "https://docker.mirrors.ustc.edu.cn",
    "https://hub-mirror.c.163.com",
    "https://mirror.baidubce.com",
    "https://ccr.ccs.tencentyun.com"
  ]
}
EOF
        sudo systemctl daemon-reload
        sudo systemctl restart docker
        sleep 5
    fi
else
    echo "📦 使用官方镜像源部署..."
    COMPOSE_FILE="docker-compose.yml"
    DOCKERFILE="Dockerfile"
fi

# 检查 .env 文件是否存在
if [ ! -f ".env" ]; then
    echo "📝 创建 .env 文件..."
    cp .env.example .env
    echo "⚠️  请编辑 .env 文件，配置必要的环境变量："
    echo "   - GITHUB_TOKEN"
    echo "   - DEEPSEEK_API_KEY"
    echo "   - WEBHOOK_SECRET"
    echo ""
    read -p "配置完成后按 Enter 继续..."
fi

# 创建日志目录
mkdir -p logs

# 清理可能存在的容器和镜像
echo "🧹 清理旧的容器和镜像..."
docker-compose -f $COMPOSE_FILE down --remove-orphans 2>/dev/null || true
docker system prune -f

# 构建并启动服务
echo "🔨 构建 Docker 镜像..."
if [ "$USE_CHINA_MIRROR" = true ]; then
    # 使用国内源构建
    docker-compose -f $COMPOSE_FILE build --no-cache
else
    # 尝试正常构建，如果失败则切换到国内源
    if ! docker-compose -f $COMPOSE_FILE build; then
        echo "⚠️  官方源构建失败，切换到国内镜像源..."
        COMPOSE_FILE="docker-compose.china.yml"
        docker-compose -f $COMPOSE_FILE build --no-cache
    fi
fi

echo "🚀 启动服务..."
docker-compose -f $COMPOSE_FILE up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 15

# 检查服务状态
echo "📊 检查服务状态..."
docker-compose -f $COMPOSE_FILE ps

# 检查 Web 服务健康状态
echo "🔍 检查 Web 服务..."
for i in {1..10}; do
    if curl -f http://localhost:8000/ > /dev/null 2>&1; then
        echo "✅ Web 服务启动成功"
        break
    else
        echo "⏳ 等待 Web 服务启动... ($i/10)"
        sleep 3
    fi
done

# 检查 Redis 连接
echo "🔍 检查 Redis 服务..."
if docker-compose -f $COMPOSE_FILE exec redis redis-cli ping > /dev/null 2>&1; then
    echo "✅ Redis 服务正常"
else
    echo "❌ Redis 服务异常"
    echo "查看日志: docker-compose -f $COMPOSE_FILE logs redis"
fi

# 检查 Celery Worker
echo "🔍 检查 Celery Worker..."
sleep 5
if docker-compose -f $COMPOSE_FILE exec worker .venv/bin/celery -A pulse_guard.worker.celery_app inspect ping > /dev/null 2>&1; then
    echo "✅ Celery Worker 正常"
else
    echo "⚠️  Celery Worker 可能需要更多时间启动"
    echo "查看日志: docker-compose -f $COMPOSE_FILE logs worker"
fi

echo ""
echo "🎉 部署完成！"
echo ""
echo "📋 服务信息："
echo "   Web 服务: http://localhost:8000"
echo "   API 文档: http://localhost:8000/docs"
echo "   GitHub Webhook: http://localhost:8000/api/webhook/github"
echo "   Gitee Webhook: http://localhost:8000/api/webhook/gitee"
echo ""
echo "📝 常用命令："
echo "   查看服务状态: docker-compose -f $COMPOSE_FILE ps"
echo "   查看日志: docker-compose -f $COMPOSE_FILE logs -f [service_name]"
echo "   停止服务: docker-compose -f $COMPOSE_FILE down"
echo "   重启服务: docker-compose -f $COMPOSE_FILE restart"
echo "   启动监控: docker-compose -f $COMPOSE_FILE --profile monitoring up -d flower"
echo ""
echo "🔧 如需启用 Flower 监控界面："
echo "   docker-compose -f $COMPOSE_FILE --profile monitoring up -d flower"
echo "   访问: http://localhost:5555"

# 显示使用的配置文件
echo ""
echo "📄 使用的配置文件: $COMPOSE_FILE"
if [ "$USE_CHINA_MIRROR" = true ]; then
    echo "🌏 已启用国内镜像源加速"
fi
