# Git
.git
.gitignore

# Python
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git
.mypy_cache
.pytest_cache
.hypothesis

# Virtual environments
.venv
.env
venv/
ENV/
env/
.venv/

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project specific
logs/
*.log
.env.local
.env.development
.env.test
.env.production

# Documentation
*.md
docs/

# Docker
Dockerfile
docker-compose*.yml
.dockerignore

# Development tools
Makefile
.pre-commit-config.yaml

# Backup files
*.bak
*.backup
*.tmp

# Node modules (if any)
node_modules/
npm-debug.log*

# Temporary files
tmp/
temp/
